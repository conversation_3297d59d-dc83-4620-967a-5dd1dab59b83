package com.yf.exam.modules.weather.scoring.engine;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;
import com.yf.exam.modules.weather.entity.WeatherScoringConfig;
import com.yf.exam.modules.weather.entity.WeatherScoringResult;
import com.yf.exam.modules.weather.entity.WeatherScoringBatchTask;
import com.yf.exam.modules.weather.mapper.WeatherHistoryExamAnswerMapper;
import com.yf.exam.modules.weather.scoring.algorithm.ComparisonResult;
import com.yf.exam.modules.weather.scoring.algorithm.WeatherComparisonResult;
import com.yf.exam.modules.weather.scoring.algorithm.WeatherDataComparator;
import com.yf.exam.modules.weather.scoring.service.ScoringConfigService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringResultService;
import com.yf.exam.modules.weather.scoring.service.WeatherScoringBatchTaskService;
import com.yf.exam.modules.weather.scoring.service.PrecipitationAreaScoringService;
import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.qu.service.QuService;
import com.yf.exam.modules.qu.entity.Qu;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 天气预报评分计算引擎
 * 核心引擎，协调各组件完成评分计算
 * 
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Component
public class WeatherScoringEngine {

    @Autowired
    private ScoringConfigService configService;
    
    @Autowired
    private WeatherDataComparator dataComparator;
    
    @Autowired
    private WeatherScoringResultService resultService;
    
    @Autowired
    private WeatherScoringBatchTaskService batchTaskService;

    @Autowired
    private PrecipitationAreaScoringService precipitationAreaScoringService;
    
    @Autowired
    private WeatherHistoryExamAnswerMapper examAnswerMapper;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private QuService quService;

    /**
     * 计算单个历史个例的评分
     * 
     * @param answerId 答案记录ID
     * @param configId 评分配置ID（可选，为空则使用当前活跃配置）
     * @return 评分结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ScoringEngineResult calculateSingleScore(String answerId, String configId) {
        ScoringEngineResult engineResult = new ScoringEngineResult();
        
        try {
            log.info("开始计算单个历史个例评分，答案ID：{}，配置ID：{}", answerId, configId);
            
            // 1. 获取答案数据
            WeatherHistoryExamAnswer answer = examAnswerMapper.selectById(answerId);
            if (answer == null) {
                return createErrorResult("答案记录不存在：" + answerId);
            }

            // 2. 获取评分配置
            WeatherScoringConfig config = getEffectiveConfig(configId);
            if (config == null) {
                return createErrorResult("无可用的评分配置");
            }

            // 3. 提取预测数据和实际数据
            Map<String, Object> predictedData = extractPredictedData(answer);
            Map<String, Object> actualData = extractActualData(answer);

            if (predictedData.isEmpty() || actualData.isEmpty()) {
                return createErrorResult("预测数据或实际数据为空");
            }

            // 4. 执行比较算法（现在使用配置文件中的权重和容差配置）
            WeatherComparisonResult comparisonResult = dataComparator.compareWeatherData(
                predictedData,
                actualData
            );

            // 5. 计算降水落区评分（如果有相关数据）
            double precipitationScore = 0.0;
            PrecipitationScoringResult precipitationResult = null;

            try {
                precipitationResult = calculatePrecipitationAreaScore(answer);
                if (precipitationResult != null && precipitationResult.isSuccess()) {
                    precipitationScore = precipitationResult.getFinalScore();
                    log.info("降水落区评分计算完成，得分：{}", precipitationScore);
                }
            } catch (Exception e) {
                log.warn("降水落区评分计算失败，将使用0分：{}", e.getMessage());
            }

            // 6. 计算最终得分（站点预报 + 降水落区）
            double stationScore = comparisonResult.getOverallScore();
            double finalScore = stationScore + precipitationScore;

            // 7. 创建评分结果记录
            WeatherScoringResult scoringResult = createScoringResult(
                answer, config, comparisonResult, finalScore, precipitationResult
            );

            // 8. 保存结果
            boolean saved = resultService.save(scoringResult);
            if (!saved) {
                return createErrorResult("保存评分结果失败");
            }

            // 9. 更新答案记录的评分状态
            updateAnswerScoringStatus(answerId, scoringResult.getId(), finalScore);

            // 10. 构建成功结果
            engineResult.setSuccess(true);
            engineResult.setScore(finalScore);
            engineResult.setScoringResultId(scoringResult.getId());
            engineResult.setComparisonResult(comparisonResult);
            engineResult.setMessage("评分计算完成");
            
            log.info("单个历史个例评分计算完成，答案ID：{}，得分：{}", answerId, finalScore);

        } catch (Exception e) {
            log.error("计算单个历史个例评分失败，答案ID：{}", answerId, e);
            engineResult.setSuccess(false);
            engineResult.setMessage("评分计算异常：" + e.getMessage());
            engineResult.setException(e);
        }

        return engineResult;
    }

    /**
     * 批量计算历史个例评分
     * 
     * @param answerIds 答案记录ID列表
     * @param configId 评分配置ID（可选）
     * @param batchName 批次名称
     * @return 批量任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String calculateBatchScore(List<String> answerIds, String configId, String batchName) {
        try {
            log.info("开始批量计算历史个例评分，任务数量：{}，配置ID：{}", answerIds.size(), configId);
            
            // 1. 创建批量任务记录
            WeatherScoringBatchTask batchTask = createBatchTask(answerIds, configId, batchName);
            boolean taskSaved = batchTaskService.save(batchTask);
            
            if (!taskSaved) {
                throw new RuntimeException("创建批量任务失败");
            }

            // 2. 异步执行批量计算（这里为了简化，使用同步方式）
            executeBatchCalculation(batchTask.getId(), answerIds, configId);
            
            log.info("批量评分任务创建完成，任务ID：{}", batchTask.getId());
            return batchTask.getId();

        } catch (Exception e) {
            log.error("创建批量评分任务失败", e);
            throw new RuntimeException("批量评分失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件批量计算评分
     * 
     * @param examId 考试ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param configId 评分配置ID（可选）
     * @param batchName 批次名称
     * @return 批量任务ID
     */
    public String calculateScoreByCondition(String examId, LocalDateTime startDate, LocalDateTime endDate,
                                          String configId, String batchName) {
        try {
            // 1. 根据条件查询答案记录
            List<String> answerIds = queryAnswerIdsByCondition(examId, startDate, endDate);
            
            if (answerIds.isEmpty()) {
                throw new RuntimeException("未找到符合条件的答案记录");
            }

            log.info("根据条件查询到{}条答案记录，开始批量评分", answerIds.size());
            
            // 2. 执行批量计算
            return calculateBatchScore(answerIds, configId, batchName);

        } catch (Exception e) {
            log.error("根据条件批量计算评分失败", e);
            throw new RuntimeException("批量评分失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算评分
     * 
     * @param answerId 答案记录ID
     * @param configId 新的评分配置ID
     * @return 评分结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ScoringEngineResult recalculateScore(String answerId, String configId) {
        try {
            log.info("开始重新计算评分，答案ID：{}，新配置ID：{}", answerId, configId);
            
            // 1. 先删除旧的评分结果（如果存在）
            resultService.deleteByAnswerId(answerId);
            
            // 2. 重新计算评分
            return calculateSingleScore(answerId, configId);

        } catch (Exception e) {
            log.error("重新计算评分失败，答案ID：{}", answerId, e);
            ScoringEngineResult result = new ScoringEngineResult();
            result.setSuccess(false);
            result.setMessage("重新计算评分失败：" + e.getMessage());
            result.setException(e);
            return result;
        }
    }

    /**
     * 获取评分结果统计
     * 
     * @param batchTaskId 批量任务ID
     * @return 统计结果
     */
    public Map<String, Object> getScoringStatistics(String batchTaskId) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 获取批量任务信息
            WeatherScoringBatchTask batchTask = batchTaskService.getById(batchTaskId);
            if (batchTask == null) {
                statistics.put("error", "批量任务不存在");
                return statistics;
            }

            // 获取该批次的所有评分结果
            List<WeatherScoringResult> results = resultService.listByBatchTaskId(batchTaskId);
            
            statistics.put("batchTask", batchTask);
            statistics.put("totalCount", batchTask.getTotalAnswers());
            statistics.put("completedCount", results.size());
            statistics.put("successCount", results.stream().mapToInt(r -> Boolean.TRUE.equals(r.getIsSuccess()) ? 1 : 0).sum());
            statistics.put("failureCount", results.stream().mapToInt(r -> Boolean.TRUE.equals(r.getIsSuccess()) ? 0 : 1).sum());
            
            if (!results.isEmpty()) {
                double avgScore = results.stream()
                                       .filter(WeatherScoringResult::getIsSuccess)
                                       .mapToDouble(WeatherScoringResult::getFinalScore)
                                       .average()
                                       .orElse(0.0);
                statistics.put("averageScore", new BigDecimal(avgScore).setScale(2, RoundingMode.HALF_UP).doubleValue());
                
                double maxScore = results.stream()
                                        .filter(WeatherScoringResult::getIsSuccess)
                                        .mapToDouble(WeatherScoringResult::getFinalScore)
                                        .max()
                                        .orElse(0.0);
                statistics.put("maxScore", maxScore);
                
                double minScore = results.stream()
                                        .filter(WeatherScoringResult::getIsSuccess)
                                        .mapToDouble(WeatherScoringResult::getFinalScore)
                                        .min()
                                        .orElse(0.0);
                statistics.put("minScore", minScore);
            }
            
            return statistics;

        } catch (Exception e) {
            log.error("获取评分统计失败，批量任务ID：{}", batchTaskId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取统计失败：" + e.getMessage());
            return errorResult;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取有效的评分配置
     */
    private WeatherScoringConfig getEffectiveConfig(String configId) {
        if (StringUtils.hasText(configId)) {
            return configService.getById(configId);
        } else {
            return configService.getActiveConfig();
        }
    }

    /**
     * 提取预测数据
     */
    private Map<String, Object> extractPredictedData(WeatherHistoryExamAnswer answer) {
        Map<String, Object> data = new HashMap<>();
        
        try {
            // 从降水答案中提取预测数据
            if (answer.getPrecipitationAnswer() != null) {
                data.putAll(answer.getPrecipitationAnswer());
            }
            
            // 从天气答案中提取预测数据
            if (answer.getWeatherAnswer() != null) {
                data.putAll(answer.getWeatherAnswer());
            }
        } catch (Exception e) {
            log.error("解析预测数据失败，答案ID：{}", answer.getId(), e);
        }
        
        return data;
    }

    /**
     * 提取实际数据（标准答案）
     */
    private Map<String, Object> extractActualData(WeatherHistoryExamAnswer answer) {
        Map<String, Object> data = new HashMap<>();

        try {
            // 从题目的scenario_data字段中获取标准答案
            Qu question = quService.getById(answer.getQuestionId());
            if (question == null) {
                log.warn("题目不存在，题目ID：{}", answer.getQuestionId());
                return data;
            }

            String scenarioData = question.getScenarioData();
            if (!StringUtils.hasText(scenarioData)) {
                log.warn("题目没有标准答案数据，题目ID：{}", answer.getQuestionId());
                return data;
            }

            // 解析scenario_data中的标准答案JSON
            Map<String, Object> standardAnswerData = objectMapper.readValue(scenarioData, Map.class);
            log.info("从题目中获取到标准答案数据：{}", standardAnswerData);

            // 提取标准答案中的stations数据
            if (standardAnswerData.containsKey("answers")) {
                data.put("stations", standardAnswerData.get("answers"));
            }

            // 如果有其他标准答案字段，也可以在这里提取
            if (standardAnswerData.containsKey("precipitation")) {
                data.put("precipitation", standardAnswerData.get("precipitation"));
            }

            if (standardAnswerData.containsKey("weather")) {
                data.put("weather", standardAnswerData.get("weather"));
            }

        } catch (Exception e) {
            log.error("解析标准答案数据失败，题目ID：{}，答案ID：{}", answer.getQuestionId(), answer.getId(), e);
        }

        return data;
    }

    /**
     * 计算降水落区评分
     * 使用新开发的PrecipitationAreaScoringService进行评分
     *
     * @param answer 考生答案记录
     * @return 降水落区评分结果
     */
    private PrecipitationScoringResult calculatePrecipitationAreaScore(WeatherHistoryExamAnswer answer) {
        try {
            log.info("开始计算降水落区评分，答案ID：{}", answer.getId());
            // 从题目数据中获取文件路径信息
            Qu question = quService.getById(answer.getQuestionId());
            if (question == null) {
                log.warn("题目不存在，无法进行降水落区评分，题目ID：{}", answer.getQuestionId());
                return null;
            }

            String scenarioData = question.getScenarioData();
            if (!StringUtils.hasText(scenarioData)) {
                log.warn("题目没有场景数据，无法进行降水落区评分，题目ID：{}", answer.getQuestionId());
                return null;
            }

            // 解析场景数据获取文件路径
            Map<String, Object> scenarioMap = objectMapper.readValue(scenarioData,
                new TypeReference<Map<String, Object>>() {});

            // 获取实况降水文件路径（MICAPS第三类文件）
            String actualFilePath = (String) scenarioMap.get("actualPrecipitationFile");
            if (!StringUtils.hasText(actualFilePath)) {
                // 尝试其他可能的字段名
                actualFilePath = (String) scenarioMap.get("precipitationAreaFilePath");
                if (!StringUtils.hasText(actualFilePath)) {
                    // 兼容旧字段名
                    actualFilePath = (String) scenarioMap.get("observationFilePath");
                    if (!StringUtils.hasText(actualFilePath)) {
                        log.warn("未找到实况降水文件路径，无法进行降水落区评分");
                        return null;
                    }
                }
            }

            // 获取CMA-MESO文件路径（MICAPS第四类文件）
            String cmaMesoFilePath = (String) scenarioMap.get("cmaMesoFile");
            if (!StringUtils.hasText(cmaMesoFilePath)) {
                // 尝试其他可能的字段名
                cmaMesoFilePath = (String) scenarioMap.get("cmaFilePath");
                if (!StringUtils.hasText(cmaMesoFilePath)) {
                    log.warn("未找到CMA-MESO文件路径，无法进行降水落区评分");
                    return null;
                }
            }

            // 获取考生的降水落区答案
            Map<String, Object> precipitationAnswer = answer.getPrecipitationAnswer();
            if (precipitationAnswer == null || precipitationAnswer.isEmpty()) {
                log.warn("考生未提交降水落区答案，无法进行评分，答案ID：{}", answer.getId());
                return null;
            }

            log.info("降水落区评分参数：实况文件={}, CMA-MESO文件={}, 答案数据大小={}",
                actualFilePath, cmaMesoFilePath, precipitationAnswer.size());

            // 调用我们新开发的降水落区评分服务
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                actualFilePath, cmaMesoFilePath, precipitationAnswer);

            if (result != null && result.isSuccess()) {
                log.info("降水落区评分计算成功，答案ID：{}，得分：{}/40", answer.getId(), result.getFinalScore());
            } else {
                log.warn("降水落区评分计算失败，答案ID：{}，原因：{}",
                    answer.getId(), result != null ? result.getMessage() : "返回结果为空");
            }

            return result;

        } catch (Exception e) {
            log.error("降水落区评分计算异常，答案ID：{}", answer.getId(), e);

            // 创建一个失败的结果对象
            PrecipitationScoringResult errorResult = new PrecipitationScoringResult();
            errorResult.setSuccess(false);
            errorResult.setMessage("降水落区评分计算异常：" + e.getMessage());
            errorResult.setFinalScore(0.0);
            return errorResult;
        }
    }

    /**
     * 创建评分结果记录
     */
    private WeatherScoringResult createScoringResult(WeatherHistoryExamAnswer answer,
                                                   WeatherScoringConfig config,
                                                   WeatherComparisonResult comparisonResult,
                                                   double finalScore,
                                                   PrecipitationScoringResult precipitationResult) throws Exception {
        WeatherScoringResult result = new WeatherScoringResult();
        
        result.setAnswerId(answer.getId());
        result.setExamId(answer.getExamId());
        result.setUserId(answer.getUserId());
        result.setQuestionId(answer.getQuestionId());
        result.setConfigId(config.getId());
        result.setConfigVersion(config.getConfigVersion());
        result.setFinalScore(finalScore);
        result.setTotalScore(BigDecimal.valueOf(finalScore)); // 设置总分，与最终得分相同
        result.setMaxScore(BigDecimal.valueOf(config.getTotalScore())); // 设置满分
        result.setScorePercentage(BigDecimal.valueOf(finalScore / config.getTotalScore() * 100).setScale(2, RoundingMode.HALF_UP)); // 计算得分率

        // 转换detailResults为Map<String, Object>格式
        Map<String, Object> detailResultsMap = new HashMap<>();
        if (comparisonResult.getDetailResults() != null) {
            comparisonResult.getDetailResults().forEach((key, value) -> {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("score", value.getScore());
                resultMap.put("match", value.isMatch());
                resultMap.put("reason", value.getReason());
                resultMap.put("predictedValue", value.getPredictedValue());
                resultMap.put("actualValue", value.getActualValue());
                resultMap.put("difference", value.getDifference());
                detailResultsMap.put(key, resultMap);
            });
        }

        // 添加降水落区评分详情
        if (precipitationResult != null && precipitationResult.isSuccess()) {
            Map<String, Object> precipitationDetailMap = new HashMap<>();
            precipitationDetailMap.put("score", precipitationResult.getFinalScore());
            precipitationDetailMap.put("totalStations", precipitationResult.getTotalStations());
            precipitationDetailMap.put("studentTSScores", precipitationResult.getStudentTSScores());
            precipitationDetailMap.put("cmaMesoTSScores", precipitationResult.getCmaMesoTSScores());
            precipitationDetailMap.put("baseScores", precipitationResult.getBaseScores());
            precipitationDetailMap.put("skillScores", precipitationResult.getSkillScores());
            precipitationDetailMap.put("summary", precipitationResult.getScoringSummary());
            detailResultsMap.put("precipitationArea", precipitationDetailMap);
        }

        result.setDetailResults(detailResultsMap);

        // 设置站点数量（目前假设为1个站点）
        result.setStationCount(1);

        // 设置分站得分详情（简化处理，使用总分）
        Map<String, Object> stationScores = new HashMap<>();
        stationScores.put("station001", finalScore);
        result.setStationScores(stationScores);

        // 设置分要素得分详情
        Map<String, Object> elementScores = new HashMap<>();
        if (comparisonResult.getDetailResults() != null) {
            comparisonResult.getDetailResults().forEach((key, value) -> {
                elementScores.put(key, value.getScore());
            });
        }
        result.setElementScores(elementScores);

        // 设置错误分析（可选）
        result.setErrorAnalysis(new HashMap<>());

        result.setIsSuccess(true);
        result.setScoringTime(new Date());
        
        return result;
    }

    /**
     * 更新答案记录的评分状态
     */
    private void updateAnswerScoringStatus(String answerId, String scoringResultId, double score) {
        try {
            examAnswerMapper.updateScoringInfo(answerId, scoringResultId, score, "已评分");
        } catch (Exception e) {
            log.error("更新答案记录评分状态失败，答案ID：{}", answerId, e);
        }
    }

    /**
     * 创建批量任务记录
     */
    private WeatherScoringBatchTask createBatchTask(List<String> answerIds, String configId, String batchName) {
        WeatherScoringBatchTask batchTask = new WeatherScoringBatchTask();
        
        batchTask.setTaskName(batchName);
        batchTask.setConfigId(configId);
        batchTask.setTotalAnswers(answerIds.size());
        batchTask.setProcessedAnswers(0);
        batchTask.setSuccessCount(0);
        batchTask.setFailCount(0);
        batchTask.setStatus("RUNNING");
        batchTask.setStartTime(new Date());
        
        return batchTask;
    }

    /**
     * 执行批量计算
     */
    private void executeBatchCalculation(String batchTaskId, List<String> answerIds, String configId) {
        int completedCount = 0;
        int successCount = 0;
        int failureCount = 0;
        
        try {
            for (String answerId : answerIds) {
                try {
                    ScoringEngineResult result = calculateSingleScore(answerId, configId);
                    completedCount++;
                    
                    if (result.isSuccess()) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                    
                    // 定期更新批量任务进度
                    if (completedCount % 10 == 0) {
                        updateBatchTaskProgress(batchTaskId, completedCount, successCount, failureCount);
                    }
                    
                } catch (Exception e) {
                    log.error("批量计算中单个答案评分失败，答案ID：{}", answerId, e);
                    completedCount++;
                    failureCount++;
                }
            }
            
            // 更新最终状态
            updateBatchTaskProgress(batchTaskId, completedCount, successCount, failureCount);
            finalizeBatchTask(batchTaskId, "COMPLETED");
            
        } catch (Exception e) {
            log.error("批量计算执行失败，任务ID：{}", batchTaskId, e);
            finalizeBatchTask(batchTaskId, "FAILED");
        }
    }

    /**
     * 更新批量任务进度
     */
    private void updateBatchTaskProgress(String batchTaskId, int completed, int success, int failure) {
        try {
            batchTaskService.updateProgress(batchTaskId, completed, success, failure);
        } catch (Exception e) {
            log.error("更新批量任务进度失败，任务ID：{}", batchTaskId, e);
        }
    }

    /**
     * 完成批量任务
     */
    private void finalizeBatchTask(String batchTaskId, String status) {
        try {
            batchTaskService.finalize(batchTaskId, status, LocalDateTime.now());
        } catch (Exception e) {
            log.error("完成批量任务失败，任务ID：{}", batchTaskId, e);
        }
    }

    /**
     * 根据条件查询答案ID
     */
    private List<String> queryAnswerIdsByCondition(String examId, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            return examAnswerMapper.queryAnswerIdsByCondition(examId, startDate, endDate);
        } catch (Exception e) {
            log.error("根据条件查询答案ID失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建错误结果
     */
    private ScoringEngineResult createErrorResult(String message) {
        ScoringEngineResult result = new ScoringEngineResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
} 