import { post, get } from '@/utils/request'
import request from '@/utils/request'

/**
 * 获取天气预报表格配置
 * @param data
 */
export function getWeatherTableConfig(data) {
  return post('/exam/api/weather/config/detail', data)
}

/**
 * 获取启用的配置列表
 */
export function getActiveConfigs() {
  return get('/exam/api/weather/config/active')
}

/**
 * 根据类型获取配置列表
 * @param templateType
 */
export function getConfigsByType(templateType) {
  return get(`/exam/api/weather/config/type/${templateType}`)
}

/**
 * 保存天气预报答案
 * @param data
 */
export function saveWeatherAnswer(data) {
  return post('/exam/api/weather/answer/save', data)
}

/**
 * 获取天气预报答案
 * @param paperQuId
 */
export function getWeatherAnswer(paperQuId) {
  return get(`/exam/api/weather/answer/${paperQuId}`)
}

/**
 * 验证天气预报答案
 * @param data
 */
export function validateWeatherAnswer(data) {
  return post('/exam/api/weather/answer/validate', data)
}

/**
 * 计算天气预报得分
 * @param data
 */
export function calculateWeatherScore(data) {
  return post('/exam/api/weather/answer/score/calculate', data)
}

/**
 * 获取表格数据结构
 * @param id
 */
export function getWeatherTableData(id) {
  return get(`/exam/api/weather/config/table-data/${id}`)
}

// ==================== 历史个例题目管理 ====================

/**
 * 分页查询历史个例题目
 * @param data
 */
export function getWeatherCaseList(data) {
  return post('/exam/api/weather/case/paging', data)
}

/**
 * 保存历史个例题目
 * @param data
 */
export function saveWeatherCase(data) {
  return post('/exam/api/weather/case/save', data)
}

/**
 * 获取历史个例题目详情（管理端，包含正确答案）
 * @param data
 */
export function getWeatherCaseDetail(data) {
  return post('/exam/api/weather/case/detail', data)
}

/**
 * 获取历史个例题目详情（考试专用，不包含正确答案）
 * @param data
 */
export function getWeatherCaseExamDetail(data) {
  return post('/exam/api/weather/case/exam-detail', data)
}

/**
 * 获取历史个例题目详情（结果查看专用，包含正确答案，需验证参与状态）
 * @param data
 */
export function getWeatherCaseResultDetail(data) {
  return post('/exam/api/weather/case/result-detail', data)
}

/**
 * 删除历史个例题目
 * @param data
 */
export function deleteWeatherCase(data) {
  return post('/exam/api/weather/case/delete', data)
}

/**
 * 上传MICAPS文件
 * @param {FormData} formData 文件数据
 */
export function uploadWeatherMicapsFile(formData) {
  return request({
    url: '/exam/api/weather/case/upload/micaps',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传历史个例实况文件
 * @param {FormData} formData 文件数据
 */
export function uploadWeatherObservationFile(formData) {
  return request({
    url: '/exam/api/weather/case/upload/observation',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传历史个例数据文件
 * @param {FormData} formData 文件数据
 */
export function uploadWeatherDataFile(formData) {
  return request({
    url: '/exam/api/weather/case/upload/data',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传降水落区文件
 * @param {FormData} formData 文件数据
 */
export function uploadPrecipitationAreaFile(formData) {
  return request({
    url: '/exam/api/weather/case/upload/precipitation-area',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 历史个例考试管理 ====================

/**
 * 分页查询历史个例考试
 * @param data
 */
export function getWeatherExamList(data) {
  return post('/exam/api/weather/exam/paging', data)
}

/**
 * 学生端分页查询历史个例考试
 * @param data
 */
export function getWeatherExamOnlineList(data) {
  return post('/exam/api/weather/exam/online-paging', data)
}

/**
 * 保存历史个例考试
 * @param data
 */
export function saveWeatherExam(data) {
  return post('/exam/api/weather/exam/save', data)
}

/**
 * 获取历史个例考试详情
 * @param data
 */
export function getWeatherExamDetail(data) {
  return post('/exam/api/weather/exam/detail', data)
}

/**
 * 删除历史个例考试
 * @param data
 */
export function deleteWeatherExam(data) {
  return post('/exam/api/weather/exam/delete', data)
}

/**
 * 更新历史个例考试状态
 * @param data
 */
export function updateWeatherExamState(data) {
  return post('/exam/api/weather/exam/state', data)
}

/**
 * 获取考试基本信息
 * @param data
 */
export function getWeatherExamInfo(data) {
  return post('/exam/api/weather/exam/info', data)
}

// ==================== 历史个例考试答案管理 ====================

/**
 * 保存历史个例考试答案
 * @param data
 */
export function saveWeatherExamAnswer(data) {
  return post('/exam/api/weather/exam/answer/save', data)
}

/**
 * 获取历史个例考试答案
 * @param data
 */
export function getWeatherExamAnswer(data) {
  return post('/exam/api/weather/exam/answer/detail', data)
}

/**
 * 提交历史个例考试
 * @param data
 */
export function submitWeatherExam(data) {
  return post('/exam/api/weather/exam/submit', data)
}

/**
 * 获取考试答题进度
 * @param data
 */
export function getWeatherExamProgress(data) {
  return post('/exam/api/weather/exam/progress', data)
}

/**
 * 获取历史个例考试结果
 * @param data
 */
export function getWeatherExamResult(data) {
  return post('/exam/api/weather/exam/result/detail', data)
}

// ==================== 站点数据管理 ====================

/**
 * 根据区域代码获取站点列表
 * @param {string} regionCode 区域代码，1-9的数字字符串
 */
export function getStationsByRegionCodes(regionCode) {
  return post('/exam/api/station/listByRegionCodes', {
    regionCode: regionCode
  })
}

/**
 * 导出历史个例考试结果
 * @param data
 */
export function exportWeatherExamResult(data) {
  return post('/exam/api/weather/exam/result/export', data, {
    responseType: 'blob'
  })
}
