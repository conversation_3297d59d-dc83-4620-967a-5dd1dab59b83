<template>
  <div class="weather-history-exam">
    <!-- 考试头部信息 -->
    <div class="exam-header">
      <div class="exam-info">
        <h2>{{ examData.title || '历史个例考试' }}</h2>
        <div class="exam-meta">
          <div class="meta-item">
            <i class="el-icon-time" />
            <span>考试时长：{{ examData.duration || examData.totalTime || 60 }}分钟</span>
          </div>
          <div class="meta-item">
            <i class="el-icon-star-on" />
            <span>总分：{{ examData.totalScore || 100 }}分</span>
          </div>
          <div class="meta-item timer">
            <i class="el-icon-timer" />
            <span>剩余时间：</span>
            <exam-timer v-model="leftSeconds" @timeout="handleTimeout" />
          </div>
        </div>
      </div>
    </div>

    <!-- 考试内容 -->
    <div class="exam-content">
      <!-- Micaps数据下载区域 - 放在第一部分上面 -->
      <div v-if="questionData.cmaFileName && questionData.cmaFilePath" class="micaps-download-section">
        <div class="download-card">
          <div class="download-header">
            <i class="el-icon-folder-opened" />
            <h3>考试数据文件</h3>
          </div>
          <div class="download-body">
            <div class="file-info">
              <div class="file-details">
                <span class="file-name">{{ questionData.cmaFileName }}</span>
                <el-tag size="mini" type="success">Micaps气象数据</el-tag>
              </div>
              <p class="file-description">包含两部分考试内容所需的完整气象数据</p>
            </div>
            <el-button
              :loading="downloadLoading"
              type="primary"
              size="large"
              icon="el-icon-download"
              class="download-btn"
              @click="downloadMicapsFile"
            >
              <span v-if="!downloadLoading">下载数据文件</span>
              <span v-else>下载中...</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 第一部分：降水分级落区预报 (40分) -->
      <el-card class="exam-section" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>第一部分：降水分级落区预报</h3>
            <el-tag type="primary" size="medium">40分</el-tag>
          </div>
        </div>

        <div class="precipitation-forecast-section">
          <!-- 题目内容和预报区域信息 - 紧凑布局 -->
          <div class="compact-info-container">
            <!-- 题目内容显示 -->
            <div class="question-content">
              <h4>题目要求：</h4>
              <p>{{ questionData.precipitationContent || '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。' }}</p>
            </div>

            <!-- 预报区域信息 -->
            <div class="region-info">
              <h4>预报区域： {{ getRegionLabel(questionData.precipitationRegion || 'region1') }}
              </h4>
              <div class="region-details">
                <span class="score-info">分值：40分</span>
              </div>
            </div>
          </div>

          <!-- 降水落区绘制组件 -->
          <div class="precipitation-drawing-container">
            <precipitation-drawing
              ref="precipitationDrawing"
              :initial-data="precipitationForecastData"
              :region="questionData.precipitationRegion || 'region1'"
              :readonly="false"
              :station-data="stationData"
              :show-stations="true"
              :station-loading="stationLoading"
              @data-change="onPrecipitationDataChange"
              @map-loaded="onMapLoaded"
            />
          </div>
        </div>
      </el-card>

      <!-- 第二部分：灾害性天气预报 (60分) -->
      <el-card class="exam-section" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>第二部分：灾害性天气预报</h3>
            <el-tag type="success" size="medium">60分</el-tag>
          </div>
        </div>

        <div class="weather-forecast-section">
          <!-- 天气预报表格 -->
          <div class="table-container">
            <weather-forecast-table
              ref="weatherTable"
              :question="questionData"
              :initial-answers="weatherAnswers"
              :readonly="false"
              @answer-change="onWeatherAnswerChange"
              @answer-save="onWeatherAnswerSave"
              @exam-submit="onWeatherExamSubmit"
            />
          </div>
        </div>
      </el-card>

      <!-- 考试操作栏 -->
      <div class="exam-actions">
        <div class="progress-section">
          <div class="progress-container">
            <div class="progress-header">
              <span class="overall-progress">总体进度：{{ overallProgress }}%</span>
              <div class="sub-progress">
                <span class="progress-item">降水：{{ precipitationProgress }}%</span>
                <span class="progress-item">天气：{{ weatherProgress }}%</span>
              </div>
            </div>
            <el-progress
              :percentage="overallProgress"
              :stroke-width="6"
              :color="progressColor"
              :show-text="false"
            />
          </div>
        </div>

        <div class="action-buttons">
          <el-button
            :loading="saving"
            size="large"
            @click="saveAnswers"
          >
            <i class="el-icon-document" />
            保存答案
          </el-button>
          <el-button
            :loading="submitting"
            :disabled="!canSubmit"
            type="primary"
            size="large"
            @click="submitExam"
          >
            <i class="el-icon-check" />
            提交考试
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ExamTimer from '@/views/paper/exam/components/ExamTimer'
import WeatherForecastTable from '@/components/WeatherForecastTable'
import PrecipitationDrawing from '@/components/weather/PrecipitationDrawing'
import tokenHelper from '@/utils/tokenHelper'
import {
  getWeatherExamInfo,
  getWeatherCaseExamDetail,
  saveWeatherExamAnswer,
  getWeatherExamAnswer,
  submitWeatherExam,
  getWeatherExamProgress,
  getStationsByRegionCodes
} from '@/api/weather/weather'

export default {
  name: 'WeatherHistoryExam',
  components: {
    ExamTimer,
    WeatherForecastTable,
    PrecipitationDrawing
  },
  data() {
    return {
      examId: '',
      examData: {},
      questionData: {
        // 设置初始默认值，确保第一部分能够显示
        precipitationContent: '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
        precipitationRegion: 'region1',
        title: '历史个例天气预报',
        content: '根据提供的气象观测资料，对指定站点进行24小时天气预报。',
        forecastDate: '2035年9月9日',
        forecastTime: '08时'
      },
      answerData: null, // 答案数据（用于时间计算）
      answerId: null, // 答案ID
      leftSeconds: 3600, // 默认60分钟
      examStartTime: null, // 考试开始时间

      // 降水预报相关
      precipitationForecastData: null,
      canUseWebGIS: true,

      // 天气预报相关
      weatherAnswers: {},

      // 状态控制
      saving: false,
      submitting: false,
      downloadLoading: false,

      // 考试进度
      precipitationProgress: 0,
      weatherProgress: 0,

      // 自动保存定时器
      autoSaveTimer: null,

      // 答案ID（用于更新已保存的答案）
      answerId: null,

      // 站点显示相关
      stationData: [], // 站点数据列表
      stationLoading: false // 站点数据加载状态
    }
  },
  computed: {
    // 降水预报状态
    precipitationStatus() {
      if (!this.precipitationForecastData) {
        return { type: 'info', text: '未开始' }
      }
      if (this.precipitationProgress < 100) {
        return { type: 'warning', text: '进行中' }
      }
      return { type: 'success', text: '已完成' }
    },

    // 总体进度
    overallProgress() {
      // 降水预报占40%，天气预报占60%
      const precipitationProg = Number(this.precipitationProgress) || 0
      const weatherProg = Number(this.weatherProgress) || 0
      const result = Math.round(precipitationProg * 0.4 + weatherProg * 0.6)
      return Math.max(0, Math.min(100, result)) // 确保在0-100范围内
    },

    // 进度条颜色
    progressColor() {
      const progress = this.overallProgress
      if (progress < 50) return '#f56c6c'
      if (progress < 80) return '#e6a23c'
      return '#67c23a'
    },

    // 是否可以提交
    canSubmit() {
      return this.overallProgress >= 80 // 至少完成80%才能提交
    }
  },
  created() {
    this.examId = this.$route.params.id
    this.initExam()
  },
  beforeDestroy() {
    // 清除自动保存定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
    }
  },
  methods: {
    // 初始化考试
    async initExam() {
      try {
        const response = await getWeatherExamInfo({ id: this.examId })

        if (response.code === 0) {
          // 修复数据结构映射：直接使用response.data
          this.examData = response.data

          // 字段名称映射：totalTime -> duration
          if (this.examData.totalTime) {
            this.examData.duration = this.examData.totalTime
          }

          // 获取题目信息
          await this.loadQuestionData()

          // 加载已保存的答案
          await this.loadSavedAnswers()

          // 在加载答案后计算剩余时间
          this.calculateRemainingTime()
        } else {
          this.$message.error(response.msg || '获取考试信息失败')
        }
      } catch (error) {
        this.$message.error('网络请求失败，请检查网络连接')
      }
    },

    // 加载题目信息
    async loadQuestionData() {
      try {
        if (this.examData.questionId) {
          // 检查Token是否有效
          if (!tokenHelper.checkBeforeApiCall(this.$router, this.$message)) {
            return
          }

          // 调用考试专用API获取题目详情（不包含正确答案）
          const response = await getWeatherCaseExamDetail({ id: this.examData.questionId })

          if (response.code === 0 && response.data) {
            const questionDetail = response.data

            // 解析scenarioData获取真实的站点列表和其他信息
            let scenarioData = {}
            try {
              if (questionDetail.scenarioData) {
                scenarioData = JSON.parse(questionDetail.scenarioData)
              }
            } catch (e) {
              scenarioData = {}
            }

            // 构建题目数据，优先使用scenarioData中的信息
            this.questionData = {
              id: questionDetail.id,
              title: questionDetail.title || '历史个例天气预报',
              content: questionDetail.content || '根据提供的气象观测资料，对指定站点进行24小时天气预报。',

              // 第一部分：降水分级落区预报内容
              precipitationContent: scenarioData.precipitationContent || '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
              precipitationRegion: scenarioData.precipitationRegion || 'region1',

              // 优先从根级别获取，如果没有则从scenarioData获取
              forecastDate: questionDetail.forecastDate || scenarioData.forecastDate || '2035年9月9日',
              forecastTime: questionDetail.forecastTime || scenarioData.forecastTime || '08时',

              // 数据文件信息
              dataFileName: scenarioData.dataFileName || questionDetail.dataFileName || null,
              dataFilePath: scenarioData.dataFilePath || questionDetail.dataFilePath || null,

              // Micaps数据文件信息
              cmaFileName: scenarioData.cmaFileName || questionDetail.cmaFileName || null,
              cmaFilePath: scenarioData.cmaFilePath || questionDetail.cmaFilePath || null,

              // 降水落区实况文件信息
              precipitationAreaFileName: scenarioData.precipitationAreaFileName || questionDetail.precipitationAreaFileName || null,
              precipitationAreaFilePath: scenarioData.precipitationAreaFilePath || questionDetail.precipitationAreaFilePath || null,

              // 重要：使用真实的站点列表
              stations: JSON.stringify(scenarioData.stations || []),

              totalScore: questionDetail.totalScore || this.examData.totalScore || 100
            }

            // 题目数据加载完成后，获取站点数据
            this.loadStationData()
          } else {
            throw new Error(response.msg || '获取题目详情失败')
          }
        } else {
          throw new Error('没有题目ID')
        }
      } catch (error) {
        console.error('获取题目详情失败:', error)

        // 使用统一的认证错误处理
        if (tokenHelper.handleAuthError(error, this.$router, this.$message)) {
          return
        }

        this.$message.warning('无法获取题目详情，使用默认配置')

        // 题目信息获取失败时使用默认数据，但给出明确提示
        this.questionData = {
          title: '历史个例天气预报',
          content: '根据提供的气象观测资料，对指定站点进行24小时天气预报。',

          // 第一部分：降水分级落区预报内容（添加默认值）
          precipitationContent: '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
          precipitationRegion: 'region1',

          forecastDate: '2035年9月9日',
          forecastTime: '08时',
          stations: JSON.stringify([]), // 修改为空数组，避免显示默认站点
          totalScore: this.examData.totalScore || 100,
          dataFileName: null,

          // 添加数据文件信息的默认值
          cmaFileName: null,
          cmaFilePath: null,
          precipitationAreaFileName: null,
          precipitationAreaFilePath: null
        }
      }
    },

    // 加载已保存的答案
    async loadSavedAnswers() {
      try {
        // 确保有questionId才调用API
        const questionId = (this.questionData && this.questionData.id) || (this.examData && this.examData.questionId)
        if (!questionId) {
          console.warn('questionId为空，无法加载已保存的答案')
          return
        }

        // 首先尝试从后端加载答案
        const response = await getWeatherExamAnswer({
          examId: this.examId,
          questionId: questionId
        })

        if ((response.code === 0 || response.code === 200 || response.success === true) && response.data) {
          const answerData = response.data

          // 保存答案数据用于时间计算
          this.answerData = answerData

          // 设置答案ID
          this.answerId = answerData.id

          // 加载降水预报答案
          if (answerData.precipitationAnswer) {
            this.precipitationForecastData = answerData.precipitationAnswer.content || {}
            this.precipitationProgress = answerData.precipitationAnswer.progress || 0

            // 强制重新渲染降水绘制组件以加载数据
            this.$nextTick(() => {
              if (this.$refs.precipitationDrawing) {
                this.$refs.precipitationDrawing.loadInitialData()
              }
            })
          }

          // 加载天气预报答案
          if (answerData.weatherAnswer && answerData.weatherAnswer.stations) {
            this.weatherAnswers = answerData.weatherAnswer.stations
            this.weatherProgress = answerData.weatherAnswer.progress || 0
          }

          // 更新整体进度
          this.updateProgress()
        } else {
          // 后端没有保存的答案，尝试从本地存储加载
          this.loadLocalAnswers()
        }
      } catch (error) {
        // 加载失败，尝试从本地存储加载
        this.loadLocalAnswers()
      }
    },

    // 从本地存储加载答案
    loadLocalAnswers() {
      const savedAnswers = localStorage.getItem(`weather_exam_${this.examId}`)
      if (savedAnswers) {
        try {
          const answers = JSON.parse(savedAnswers)

          // 加载天气预报答案
          if (answers.weatherAnswer && answers.weatherAnswer.stations) {
            this.weatherAnswers = answers.weatherAnswer.stations
          } else if (answers.weather) {
            // 兼容旧格式
            this.weatherAnswers = answers.weather
          }

          // 加载降水预报答案
          if (answers.precipitationAnswer && answers.precipitationAnswer.content) {
            this.precipitationForecastData = answers.precipitationAnswer.content
          } else if (answers.precipitation) {
            // 兼容旧格式
            this.precipitationForecastData = answers.precipitation
          }

          this.updateProgress()
        } catch (error) {
        }
      }
    },

    // 处理降水数据变更
    onPrecipitationDataChange(data) {
      // 更新降水预报数据
      this.precipitationForecastData = data

      // 计算进度
      this.precipitationProgress = data.totalCount > 0 ? 100 : 0

      // 更新整体进度
      this.updateProgress()

      // 使用防抖的自动保存
      this.autoSave()
    },

    // 检查是否为Micaps数据文件
    isMicapsFile(fileName) {
      if (!fileName) return false

      // Micaps数据文件的常见扩展名
      const micapsExtensions = ['.dat', '.txt', '.000', '.001', '.002', '.003', '.004', '.005', '.006', '.007', '.008', '.009', '.010', '.011', '.012', '.013', '.014', '.015', '.016', '.017', '.018', '.019', '.020', '.021', '.022', '.023']

      const lowerFileName = fileName.toLowerCase()
      return micapsExtensions.some(ext => lowerFileName.endsWith(ext))
    },

    // 下载Micaps数据文件
    downloadMicapsFile() {
      if (!this.questionData.cmaFileName || !this.questionData.cmaFilePath) {
        this.$message.warning('没有可下载的Micaps数据文件')
        return
      }

      this.downloadLoading = true

      try {
        // 创建临时链接进行下载
        const link = document.createElement('a')
        link.href = this.questionData.cmaFilePath
        link.download = this.questionData.cmaFileName
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success('开始下载Micaps气象数据文件')
      } catch (error) {
        this.$message.error('Micaps数据文件下载失败')
      } finally {
        this.downloadLoading = false
      }
    },

    // 天气答案变化
    onWeatherAnswerChange(changeData) {
      // 保存考生答案（八方位风向直接存储）
      this.weatherAnswers = { ...changeData.allAnswers }
      this.updateWeatherProgress()
      this.autoSave()
    },

    // 天气预报答案保存
    onWeatherAnswerSave(answers) {
      this.weatherAnswers = answers
      this.updateWeatherProgress()
      this.$message.success('天气预报答案已保存')
    },

    // 天气预报考试提交
    async onWeatherExamSubmit(answers) {
      // 先保存答案
      this.weatherAnswers = answers
      this.updateWeatherProgress()

      // 然后提交整个考试
      await this.submitExam()
    },

    // 获取区域标签
    getRegionLabel(regionValue) {
      const regionMap = {
        'region1': '区域一(华北区域)',
        'region2': '区域二(东北区域)',
        'region3': '区域三(长江中下游区域)',
        'region4': '区域四(华南区域)',
        'region5': '区域五(西南地区东部)',
        'region6': '区域六(青藏高原区域)',
        'region7': '区域七(新疆区域)',
        'region8': '区域八(西北地区东部区域)',
        'region9': '区域九(内蒙古区域)'
      }
      return regionMap[regionValue] || regionValue
    },

    // 提取区域代码
    extractRegionCode(regionValue) {
      // 从 'region1' 提取 '1'
      if (regionValue && regionValue.startsWith('region')) {
        return regionValue.replace('region', '')
      }
      return '1' // 默认区域1
    },

    // 获取站点数据
    async loadStationData() {
      try {
        this.stationLoading = true
        const regionCode = this.extractRegionCode(this.questionData.precipitationRegion)

        const response = await getStationsByRegionCodes(regionCode)

        if (response.code === 0 && response.data) {
          this.stationData = response.data
        } else {
          console.warn('获取站点数据失败:', response.msg)
          this.$message.warning('获取站点数据失败')
        }
      } catch (error) {
        console.error('获取站点数据异常:', error)
        this.$message.error('获取站点数据异常: ' + (error.message || '网络错误'))
      } finally {
        this.stationLoading = false
      }
    },

    // 处理降水绘制组件地图加载完成事件
    onMapLoaded() {
      console.log('地图加载完成，开始获取站点数据')
      this.loadStationData()
    },

    // 更新降水预报进度
    updatePrecipitationProgress() {
      // 降水落区绘制的进度基于绘制的落区数量
      if (this.precipitationForecastData && this.precipitationForecastData.totalCount > 0) {
        this.precipitationProgress = 100
      } else {
        this.precipitationProgress = 0
      }
    },

    // 更新天气预报进度
    updateWeatherProgress() {
      try {
        const stations = this.questionData.stations ? JSON.parse(this.questionData.stations) : []
        const elements = ['windForce', 'windDirection', 'minTemperature', 'maxTemperature', 'precipitation', 'disasterWeather']

        let answered = 0
        const total = stations.length * elements.length

        stations.forEach(station => {
          elements.forEach(element => {
            if (this.weatherAnswers[station] && this.weatherAnswers[station][element]) {
              answered++
            }
          })
        })

        const progress = total > 0 ? Math.round((answered / total) * 100) : 0
        this.weatherProgress = Math.max(0, Math.min(100, progress)) // 确保在0-100范围内
      } catch (error) {
        this.weatherProgress = 0
      }
    },

    // 更新总体进度
    updateProgress() {
      this.updatePrecipitationProgress()
      this.updateWeatherProgress()
      // overallProgress 是computed属性，会自动计算
    },

    // 自动保存
    autoSave() {
      clearTimeout(this.autoSaveTimer)
      this.autoSaveTimer = setTimeout(() => {
        this.saveAnswers(false) // 静默保存
      }, 5000)
    },

    // 保存答案
    async saveAnswers(showMessage = true) {
      this.saving = true
      try {
        // 准备答案数据
        const answerData = {
          examId: this.examId,
          questionId: this.questionData.id,
          answerId: this.answerId, // 如果是更新已有答案

          // 第一部分：降水分级落区预报答案
          precipitationAnswer: {
            content: this.precipitationForecastData || {},
            progress: this.precipitationProgress,
            status: this.precipitationProgress >= 100 ? 'completed' : 'in_progress'
          },

          // 第二部分：灾害性天气预报答案
          weatherAnswer: {
            stations: this.getWeatherStationAnswers(), // 考生答案（八方位风向直接存储）
            progress: this.weatherProgress,
            status: this.weatherProgress >= 100 ? 'completed' : 'in_progress'
          },

          // 整体进度
          overallProgress: this.overallProgress,

          // 答题时间信息
          answerTime: new Date().toISOString(),
          timeSpent: this.getTimeSpent(), // 已用时间（秒）
          examStartTime: this.examStartTime // 考试开始时间
        }

        // 调用后端API保存答案

        // 检查getWeatherStationAnswers的返回值
        const stationAnswers = this.getWeatherStationAnswers()

        const response = await saveWeatherExamAnswer(answerData)

        if (response.code === 0 || response.code === 200 || response.success === true) {
          // 保存成功，更新answerId
          if (response.data && response.data.id) {
            this.answerId = response.data.id
          }

          // 同时保存到本地存储作为备份
          localStorage.setItem(`weather_exam_${this.examId}`, JSON.stringify(answerData))

          if (showMessage) {
            this.$message.success('答案保存成功')
          }
        } else {
          throw new Error(response.msg || '保存失败')
        }
      } catch (error) {
        // 保存失败时，至少保存到本地存储
        try {
          const backupData = {
            examId: this.examId,
            questionId: this.questionData.id,
            precipitationAnswer: { content: this.precipitationForecastData },
            weatherAnswer: { stations: this.getWeatherStationAnswers() },
            overallProgress: this.overallProgress,
            backupTime: new Date().toISOString()
          }
          localStorage.setItem(`weather_exam_backup_${this.examId}`, JSON.stringify(backupData))
        } catch (localError) {
        }

        if (showMessage) {
          this.$message.error('保存答案失败：' + (error.message || '网络错误'))
        }
      } finally {
        this.saving = false
      }
    },

    // 获取天气预报站点答案
    getWeatherStationAnswers() {
      // 直接使用weatherAnswers数据，这个数据已经通过onWeatherAnswerChange方法实时更新
      if (this.weatherAnswers && Object.keys(this.weatherAnswers).length > 0) {
        return this.weatherAnswers
      }

      // 如果weatherAnswers为空，返回空对象
      return {}
    },

    // 提交考试
    async submitExam(isAutoSubmit = false) {
      if (!this.canSubmit && !isAutoSubmit) {
        this.$message.warning('请完成至少80%的答题内容后再提交')
        return
      }

      // 如果不是自动提交，需要用户确认
      if (!isAutoSubmit) {
        try {
          const confirmResult = await this.$confirm(
            `确认提交考试吗？\n当前进度：${this.overallProgress}%\n提交后将无法修改答案。`,
            '确认提交',
            {
              confirmButtonText: '确定提交',
              cancelButtonText: '继续答题',
              type: 'warning'
            }
          )

          if (confirmResult !== 'confirm') {
            return
          }
        } catch (error) {
          // 用户取消了确认对话框
          return
        }
      }

      this.submitting = true
      try {
        // 先尝试保存答案，但不阻塞提交流程
        try {
          await this.saveAnswers(false)
        } catch (saveError) {
        }

        // 准备提交数据
        const submitData = {
          examId: this.examId,
          questionId: this.questionData.id,
          answerId: this.answerId || null, // 允许为空，后端会处理
          finalProgress: this.overallProgress,
          submitTime: new Date().toISOString(),
          totalTimeSpent: this.getTimeSpent(),
          examStartTime: this.examStartTime,

          // 如果没有answerId，直接在提交时包含答案数据
          answerData: !this.answerId ? {
            precipitationAnswer: {
              content: this.precipitationForecastData || {},
              progress: this.precipitationProgress,
              status: this.precipitationProgress >= 100 ? 'completed' : 'in_progress'
            },
            weatherAnswer: {
              stations: this.getWeatherStationAnswers(),
              progress: this.weatherProgress,
              status: this.weatherProgress >= 100 ? 'completed' : 'in_progress'
            },
            overallProgress: this.overallProgress,
            answerTime: new Date().toISOString(),
            timeSpent: this.getTimeSpent(),
            examStartTime: this.examStartTime
          } : null
        }

        // 调用后端API提交考试
        const response = await submitWeatherExam(submitData)

        if (response.code === 0 || response.code === 200 || response.success === true) {
          this.$message.success('考试提交成功！')

          // 清除本地存储的答案和开始时间
          localStorage.removeItem(`weather_exam_${this.examId}`)
          localStorage.removeItem(`weather_exam_backup_${this.examId}`)
          localStorage.removeItem(`weather_exam_start_time_${this.examId}`)

          // 跳转到历史个例考试列表页面
          this.$router.push('/my/weather')
        } else {
          throw new Error(response.msg || '提交失败')
        }
      } catch (error) {
        console.error('提交考试失败:', error)
        this.$message.error('提交考试失败：' + (error.message || '网络错误'))
      } finally {
        this.submitting = false
      }
    },

    // 获取已用时间（秒）
    getTimeSpent() {
      if (this.examStartTime) {
        const startTime = new Date(this.examStartTime)
        const currentTime = new Date()
        return Math.floor((currentTime - startTime) / 1000)
      }
      // 如果没有开始时间，使用剩余时间计算
      const totalTimeMinutes = this.examData.duration || this.examData.totalTime || 60
      const totalTimeSeconds = totalTimeMinutes * 60
      return totalTimeSeconds - this.leftSeconds
    },

    // 考试超时
    handleTimeout() {
      this.$message.warning('考试时间已到，系统将自动提交')
      this.submitExam(true) // 传入 true 表示自动提交
    },

    // 计算剩余时间
    calculateRemainingTime() {
      const totalTimeMinutes = this.examData.duration || this.examData.totalTime || 60
      const totalTimeSeconds = totalTimeMinutes * 60

      // 确定考试开始时间
      let startTime = null

      // 优先使用已记录的考试开始时间
      if (this.examStartTime) {
        startTime = new Date(this.examStartTime)
      }
      // 如果没有记录的开始时间，但有答案数据，使用答案的创建时间
      else if (this.answerData && this.answerData.createTime) {
        startTime = new Date(this.answerData.createTime)
        // 记录这个时间作为考试开始时间
        this.examStartTime = this.answerData.createTime
        // 同时保存到本地存储
        localStorage.setItem(`weather_exam_start_time_${this.examId}`, this.answerData.createTime)
      }
      // 尝试从本地存储获取开始时间
      else {
        const savedStartTime = localStorage.getItem(`weather_exam_start_time_${this.examId}`)
        if (savedStartTime) {
          startTime = new Date(savedStartTime)
          this.examStartTime = savedStartTime
        }
      }

      if (startTime) {
        const currentTime = new Date()
        const elapsedSeconds = Math.floor((currentTime - startTime) / 1000)

        // 计算剩余时间
        const remainingSeconds = Math.max(0, totalTimeSeconds - elapsedSeconds)
        this.leftSeconds = remainingSeconds

        console.log('考试开始时间:', startTime)
        console.log('当前时间:', currentTime)
        console.log('已用时间:', elapsedSeconds, '秒')
        console.log('剩余时间:', remainingSeconds, '秒')

        // 如果时间已经用完，自动提交
        if (remainingSeconds <= 0) {
          this.$nextTick(() => {
            this.handleTimeout()
          })
        }
      } else {
        // 第一次进入考试，记录开始时间并使用完整时间
        const now = new Date().toISOString()
        this.examStartTime = now
        localStorage.setItem(`weather_exam_start_time_${this.examId}`, now)
        this.leftSeconds = totalTimeSeconds
        console.log('第一次进入考试，记录开始时间:', now)
        console.log('剩余时间:', totalTimeSeconds, '秒')
      }
    }

  }
}
</script>

<style scoped>
.weather-history-exam {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
}

.weather-history-exam::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.exam-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  color: #2c3e50;
  padding: 25px;
  border-radius: 20px;
  margin-bottom: 25px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.exam-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.exam-info h2 {
  margin: 0 0 20px 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.exam-meta {
  display: flex;
  gap: 35px;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
  font-weight: 500;
  padding: 8px 16px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 25px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

/* 取消meta-item悬停抖动效果 */
.meta-item:hover {
  background: rgba(102, 126, 234, 0.2);
  /* transform: translateY(-2px); 移除抖动效果 */
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.meta-item i {
  font-size: 16px;
  color: #667eea;
}

.meta-item.timer {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  color: white;
  border: 1px solid rgba(255, 107, 107, 0.3);
  font-weight: 600;
}

.meta-item.timer i {
  color: #ffffff;
}

.exam-content {
  margin-bottom: 25px;
  position: relative;
  z-index: 1;
}

.exam-section {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

/* 取消卡片悬停抖动效果，避免影响考生答题 */
.exam-section:hover {
  /* transform: translateY(-5px); 移除抖动效果 */
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.15),
    0 12px 24px rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 20px 25px 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: slideShine 2s infinite;
}

@keyframes slideShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.section-title h3 {
  margin: 0;
  color: white;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title h3::before {
  content: '🌦️';
  font-size: 24px;
}

/* 第二部分使用不同的图标 */
.exam-section:nth-child(3) .section-title h3::before {
  content: '⚡';
}

.section-description {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.precipitation-forecast-section {
  padding: 25px;
}

/* 紧凑信息容器 - 横向布局 */
.compact-info-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.question-content {
  flex: 2;
  padding: 20px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 16px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  margin-bottom: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.1);
}

.question-content::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #409EFF, #667eea);
  border-radius: 16px;
  z-index: -1;
  opacity: 0.1;
}

.question-content h4 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-content h4::before {
  content: '📝';
  font-size: 18px;
}

.question-content p {
  margin: 0;
  color: #37474f;
  line-height: 1.7;
  font-size: 14px;
  font-weight: 500;
}

.region-info {
  flex: 1;
  padding: 20px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 16px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  margin-bottom: 0;
  min-width: 250px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(103, 194, 58, 0.1);
}

.region-info::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-radius: 16px;
  z-index: -1;
  opacity: 0.1;
}

.region-info h4 {
  margin: 0 0 12px 0;
  color: #388e3c;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-info h4::before {
  content: '🗺️';
  font-size: 18px;
}

.region-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.score-info {
  color: #558b2f;
  font-size: 14px;
  font-weight: 600;
  padding: 6px 12px;
  background: rgba(103, 194, 58, 0.1);
  border-radius: 20px;
  text-align: center;
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.forecast-description {
  margin-bottom: 20px;
}

.forecast-description h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.forecast-description ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.forecast-description li {
  margin-bottom: 5px;
}

.webgis-integration {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background-color: #fafbfc;
  transition: all 0.3s ease;
}

.webgis-integration:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.precipitation-drawing-container {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.precipitation-drawing-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: gradientMove 3s ease-in-out infinite;
}

@keyframes gradientMove {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.webgis-placeholder {
  margin-bottom: 20px;
}

.webgis-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 15px;
}

.webgis-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.webgis-content p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 14px;
}

.forecast-status {
  display: flex;
  justify-content: center;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.status-item, .forecast-summary {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-label {
  color: #666;
  font-size: 14px;
}

.weather-forecast-section {
  padding: 25px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  margin: 20px 0;
}

/* 第二部分表格样式优化 */
.weather-forecast-section .table-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* CSS重置：消除所有可能的默认边距和填充 */
.weather-forecast-section ::v-deep .el-table,
.weather-forecast-section ::v-deep .el-table *,
.weather-forecast-section ::v-deep .el-input-number,
.weather-forecast-section ::v-deep .el-input-number *,
.weather-forecast-section ::v-deep .el-input,
.weather-forecast-section ::v-deep .el-input *,
.weather-forecast-section ::v-deep .el-select,
.weather-forecast-section ::v-deep .el-select * {
  box-sizing: border-box !important;
}

.weather-forecast-section ::v-deep .el-input-number,
.weather-forecast-section ::v-deep .el-input,
.weather-forecast-section ::v-deep .el-select {
  margin: 0 !important;
  padding: 0 !important;
}

.weather-forecast-section ::v-deep .el-table__row {
  height: 50px !important;
}

.weather-forecast-section ::v-deep .el-table td,
.weather-forecast-section ::v-deep .el-table th {
  padding: 6px 5px !important;
  vertical-align: middle;
  line-height: 1.5;
}

.weather-forecast-section ::v-deep .el-table__header-wrapper .el-table__header th {
  height: 60px !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  font-weight: 700;
  font-size: 15px;
}

.weather-forecast-section ::v-deep .el-input__inner,
.weather-forecast-section ::v-deep .el-select .el-input__inner,
.weather-forecast-section ::v-deep .el-input-number .el-input__inner {
  height: 42px !important;
  line-height: 42px !important;
  font-size: 14px;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  transition: all 0.3s ease;
}

/* 修复输入框容器高度，消除底部空白 */
.weather-forecast-section ::v-deep .el-input-number,
.weather-forecast-section ::v-deep .el-input,
.weather-forecast-section ::v-deep .el-select {
  height: 42px !important;
  margin: 0 !important;
}

.weather-forecast-section ::v-deep .el-input-number .el-input,
.weather-forecast-section ::v-deep .el-input-number .el-input__wrapper {
  height: 42px !important;
}

/* 进一步修复数字输入框的高度和边距问题 */
.weather-forecast-section ::v-deep .el-input-number__increase,
.weather-forecast-section ::v-deep .el-input-number__decrease {
  height: 31px !important;
  line-height: 21px !important;
}

.weather-forecast-section ::v-deep .el-form-item {
  margin-bottom: 0 !important;
}

.weather-forecast-section ::v-deep .el-form-item__content {
  line-height: 42px !important;
  margin: 0 !important;
}

/* 确保表格单元格内容垂直居中且无多余空白 */
.weather-forecast-section ::v-deep .el-table .cell {
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 强制修复输入单元格的所有元素高度 */
.weather-forecast-section ::v-deep .input-cell {
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
}

.weather-forecast-section ::v-deep .input-cell > * {
  height: 42px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.weather-forecast-section ::v-deep .input-cell .el-input-number,
.weather-forecast-section ::v-deep .input-cell .el-input,
.weather-forecast-section ::v-deep .input-cell .el-select {
  height: 42px !important;
  min-height: 42px !important;
  max-height: 42px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
}

/* 特别处理数字输入框内部结构 */
.weather-forecast-section ::v-deep .input-cell .el-input-number .el-input {
  height: 42px !important;
  margin: 0 !important;
}

.weather-forecast-section ::v-deep .input-cell .el-input-number .el-input .el-input__inner {
  height: 42px !important;
  line-height: 42px !important;
  margin: 0 !important;
  border-radius: 8px !important;
}

.weather-forecast-section ::v-deep .input-cell .el-input-number__increase,
.weather-forecast-section ::v-deep .input-cell .el-input-number__decrease {
  height: 31px !important;
  line-height: 19px !important;
  right: 1px !important;
}

/* 修复可能存在的表单项边距 */
.weather-forecast-section ::v-deep .input-cell .el-form-item {
  margin: 0 !important;
  height: 42px !important;
}

.weather-forecast-section ::v-deep .input-cell .el-form-item__content {
  height: 42px !important;
  line-height: 42px !important;
  margin: 0 !important;
}

/* 最强制的修复：直接覆盖所有可能的空白 */
.weather-forecast-section ::v-deep td.input-cell {
  height: 50px !important;
  padding: 4px 5px !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
}

.weather-forecast-section ::v-deep td.input-cell * {
  box-sizing: border-box !important;
}

/* 确保温度输入框完全填充单元格 */
.weather-forecast-section ::v-deep .input-cell .el-input-number {
  width: 100% !important;
  height: 42px !important;
  display: block !important;
}

.weather-forecast-section ::v-deep .input-cell .el-input-number .el-input {
  width: 100% !important;
  height: 42px !important;
}

.weather-forecast-section ::v-deep .input-cell .el-input-number .el-input__inner {
  width: 100% !important;
  height: 42px !important;
  box-sizing: border-box !important;
  padding: 0 30px 0 15px !important;
  font-size: 14px !important;
  text-align: center !important;
}

/* 覆盖Element UI的size="small"样式 */
.weather-forecast-section ::v-deep .el-input-number--small {
  width: 100% !important;
  height: 42px !important;
}

.weather-forecast-section ::v-deep .el-input-number--small .el-input__inner {
  height: 42px !important;
  line-height: 42px !important;
  font-size: 14px !important;
}

.weather-forecast-section ::v-deep .el-input-number--small .el-input-number__increase,
.weather-forecast-section ::v-deep .el-input-number--small .el-input-number__decrease {
  width: 28px !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 12px !important;
}

/* 同样处理选择框的size="small"样式 */
.weather-forecast-section ::v-deep .el-select--small {
  width: 100% !important;
  height: 42px !important;
}

.weather-forecast-section ::v-deep .el-select--small .el-input {
  height: 42px !important;
}

.weather-forecast-section ::v-deep .el-select--small .el-input__inner {
  height: 42px !important;
  line-height: 42px !important;
  font-size: 14px !important;
  text-align: center !important;
}

/* 修复所有small尺寸组件的容器 */
.weather-forecast-section ::v-deep .input-cell .el-input--small,
.weather-forecast-section ::v-deep .input-cell .el-select--small,
.weather-forecast-section ::v-deep .input-cell .el-input-number--small {
  margin: 0 !important;
  padding: 0 !important;
}

/* 彻底修复温度输入框内部空白 */
.weather-forecast-section ::v-deep .input-cell {
  display: flex !important;
  align-items: stretch !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  height: 70px !important;
}

.weather-forecast-section ::v-deep .input-cell > * {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 42px !important;
}

/* 强制覆盖Element UI默认的small尺寸边距 */
.weather-forecast-section ::v-deep .el-input-number.el-input-number--small,
.weather-forecast-section ::v-deep .el-select.el-select--small,
.weather-forecast-section ::v-deep .el-input.el-input--small {
  margin: 0 !important;
  padding: 0 !important;
  height: 42px !important;
  line-height: 42px !important;
}

/* 全局移除所有可能导致抖动的transform动画 */
.weather-history-exam * {
  transform: none !important;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* 允许必要的动画，但禁用transform */
.weather-history-exam .el-button,
.weather-history-exam .el-input,
.weather-history-exam .el-select {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

.weather-forecast-section ::v-deep .el-input__inner:focus,
.weather-forecast-section ::v-deep .el-select .el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.weather-forecast-section ::v-deep .el-select {
  width: 100%;
}

.weather-forecast-section ::v-deep .el-input-number {
  width: 100%;
}

/* 表格行悬停效果 */
.weather-forecast-section ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(102, 126, 234, 0.05) !important;
}

/* 表格边框优化 */
.weather-forecast-section ::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.weather-forecast-section ::v-deep .el-table td,
.weather-forecast-section ::v-deep .el-table th.is-leaf {
  border-bottom: 1px solid #e9ecef;
}

.weather-forecast-section ::v-deep .el-table--border .el-table__cell {
  border-right: 1px solid #e9ecef;
}

/* 表格内容居中对齐 */
.weather-forecast-section ::v-deep .el-table .cell {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

/* 优化选择框和输入框的样式 */
.weather-forecast-section ::v-deep .el-select .el-input.is-focus .el-input__inner,
.weather-forecast-section ::v-deep .el-input.is-focus .el-input__inner,
.weather-forecast-section ::v-deep .el-input-number.is-focus .el-input__inner {
  border-color: #667eea;
}

.weather-forecast-section ::v-deep .el-select-dropdown {
  border: 1px solid #e0e6ed;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.weather-forecast-section ::v-deep .el-select-dropdown__item:hover {
  background-color: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.data-file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  color: #666;
}

.exam-actions {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  padding: 25px;
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  margin-top: 25px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 精美的进度显示样式 */
.progress-section {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.progress-container {
  max-width: 650px;
  margin: 0 auto;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.overall-progress {
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sub-progress {
  display: flex;
  gap: 12px;
}

.progress-item {
  font-size: 13px;
  color: #667eea;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 255, 0.9) 100%);
  padding: 6px 12px;
  border-radius: 20px;
  white-space: nowrap;
  font-weight: 600;
  border: 1px solid rgba(102, 126, 234, 0.2);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

/* 取消进度项悬停抖动效果 */
.progress-item:hover {
  /* transform: translateY(-1px); 移除抖动效果 */
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.action-buttons .el-button {
  min-width: 140px;
  height: 45px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-buttons .el-button:not(.el-button--primary) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
}

/* 取消按钮悬停抖动效果 */
.action-buttons .el-button:not(.el-button--primary):hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  /* transform: translateY(-2px); 移除抖动效果 */
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.action-buttons .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.action-buttons .el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  /* transform: translateY(-2px); 移除抖动效果 */
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

/* Micaps数据下载区域样式 */
.micaps-download-section {
  margin-bottom: 25px;
}

.download-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.download-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.download-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.download-header i {
  font-size: 24px;
  color: #FFD700;
}

.download-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.download-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.file-info {
  flex: 1;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.file-name {
  font-weight: 600;
  color: white;
  font-size: 16px;
}

.file-description {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.4;
}

.download-btn {
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  min-width: 160px;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
  background: linear-gradient(45deg, #FF5252, #FF7043);
}

.download-btn:active {
  transform: translateY(0);
}

.download-btn.is-loading {
  background: linear-gradient(45deg, #95A5A6, #BDC3C7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-meta {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .forecast-status {
    flex-direction: column;
    gap: 10px;
  }

  /* 移动端紧凑信息容器垂直布局 */
  .compact-info-container {
    flex-direction: column;
    gap: 15px;
  }

  .region-info {
    min-width: auto;
  }

  .question-content p {
    font-size: 13px;
  }

  /* 移动端确保没有任何抖动效果 */
  .exam-section:hover {
    transform: none !important;
  }

  .meta-item:hover {
    transform: none !important;
  }

  .progress-item:hover {
    transform: none !important;
  }

  .action-buttons .el-button:hover {
    transform: none !important;
  }

  .download-card {
    padding: 20px;
  }

  .download-body {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .file-details {
    justify-content: center;
  }

  .download-btn {
    width: 100%;
    min-width: auto;
  }

  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .sub-progress {
    gap: 10px;
  }

  .progress-item {
    font-size: 12px;
    padding: 1px 6px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
